local FreeCam = {}
local isFreeCamActive = false
local freeCamEntity = nil
local initialCamCoords = nil
local initialCamRot = nil

-- Freecam nastavení
local MOVE_SPEED = 1.0
local FAST_MOVE_SPEED = 5.0
local ROTATION_SPEED = 2.0

function FreeCam.IsActive()
    return isFreeCamActive
end

function FreeCam.Start(coords)
    if isFreeCamActive then return end
    
    local playerPed = PlayerPedId()
    local playerCoords = coords or GetEntityCoords(playerPed)
    
    -- Uložit původní pozici kamery
    initialCamCoords = GetGameplayCamCoords()
    initialCamRot = GetGameplayCamRot(2)
    
    -- Vytvořit neviditelný objekt pro kameru
    freeCamEntity = CreateObject(GetHashKey('prop_dummy_01'), playerCoords.x, playerCoords.y, playerCoords.z + 50.0, false, false, false)
    SetEntityVisible(freeCamEntity, false, false)
    SetEntityCollision(freeCamEntity, false, false)
    FreezeEntityPosition(freeCamEntity, true)
    
    -- Nastavit kameru
    SetEntityCoords(freeCamEntity, playerCoords.x, playerCoords.y, playerCoords.z + 50.0)
    AttachCamToEntity(freeCamEntity, 0.0, 0.0, 0.0, true)
    
    -- Zmrazit hráče
    FreezeEntityPosition(playerPed, true)
    SetEntityVisible(playerPed, false, false)
    
    isFreeCamActive = true
    
    -- Spustit update loop
    CreateThread(function()
        while isFreeCamActive do
            FreeCam.Update()
            Wait(0)
        end
    end)
end

function FreeCam.Stop()
    if not isFreeCamActive then return end
    
    local playerPed = PlayerPedId()
    
    -- Obnovit hráče
    FreezeEntityPosition(playerPed, false)
    SetEntityVisible(playerPed, true, false)
    
    -- Smazat freecam objekt
    if DoesEntityExist(freeCamEntity) then
        DeleteEntity(freeCamEntity)
        freeCamEntity = nil
    end
    
    -- Obnovit kameru
    if initialCamCoords and initialCamRot then
        SetGameplayCamRelativeHeading(0.0)
        SetGameplayCamRelativePitch(0.0, 1.0)
    end
    
    isFreeCamActive = false
end

function FreeCam.Update()
    if not isFreeCamActive or not DoesEntityExist(freeCamEntity) then return end
    
    local camCoords = GetEntityCoords(freeCamEntity)
    local camRot = GetEntityRotation(freeCamEntity, 2)
    
    -- Pohyb kamery
    local moveSpeed = MOVE_SPEED
    if IsControlPressed(0, 21) then -- Left Shift - rychlejší pohyb
        moveSpeed = FAST_MOVE_SPEED
    end
    
    -- Dopředu/Dozadu
    if IsControlPressed(0, 32) then -- W
        local forward = GetEntityForwardVector(freeCamEntity)
        SetEntityCoords(freeCamEntity, camCoords + forward * moveSpeed)
    end
    if IsControlPressed(0, 33) then -- S
        local forward = GetEntityForwardVector(freeCamEntity)
        SetEntityCoords(freeCamEntity, camCoords - forward * moveSpeed)
    end
    
    -- Vlevo/Vpravo
    if IsControlPressed(0, 34) then -- A
        local right = GetEntityRightVector(freeCamEntity)
        SetEntityCoords(freeCamEntity, camCoords - right * moveSpeed)
    end
    if IsControlPressed(0, 35) then -- D
        local right = GetEntityRightVector(freeCamEntity)
        SetEntityCoords(freeCamEntity, camCoords + right * moveSpeed)
    end
    
    -- Nahoru/Dolů
    if IsControlPressed(0, Config.Editor.keybind.up) then -- Q
        SetEntityCoords(freeCamEntity, camCoords.x, camCoords.y, camCoords.z + moveSpeed)
    end
    if IsControlPressed(0, Config.Editor.keybind.down) then -- Z
        SetEntityCoords(freeCamEntity, camCoords.x, camCoords.y, camCoords.z - moveSpeed)
    end
    
    -- Rotace myší
    local mouseX = GetDisabledControlNormal(0, 1) * ROTATION_SPEED
    local mouseY = GetDisabledControlNormal(0, 2) * ROTATION_SPEED
    
    local newRotX = camRot.x - mouseY
    local newRotZ = camRot.z - mouseX
    
    -- Omezit vertikální rotaci
    if newRotX > 90.0 then newRotX = 90.0 end
    if newRotX < -90.0 then newRotX = -90.0 end
    
    SetEntityRotation(freeCamEntity, newRotX, camRot.y, newRotZ, 2, true)
    
    -- Zakázat některé kontroly
    DisableAllControlActions(0)
    EnableControlAction(0, 1, true) -- Mouse look
    EnableControlAction(0, 2, true) -- Mouse look
    EnableControlAction(0, 32, true) -- W
    EnableControlAction(0, 33, true) -- S
    EnableControlAction(0, 34, true) -- A
    EnableControlAction(0, 35, true) -- D
    EnableControlAction(0, 21, true) -- Left Shift
    EnableControlAction(0, Config.Editor.keybind.up, true) -- Q
    EnableControlAction(0, Config.Editor.keybind.down, true) -- Z
    EnableControlAction(0, Config.Editor.keybind.confirm, true) -- E
    EnableControlAction(0, Config.Editor.keybind.cancel, true) -- X
end

function FreeCam.GetCoords()
    if not isFreeCamActive or not DoesEntityExist(freeCamEntity) then
        return GetEntityCoords(PlayerPedId())
    end
    return GetEntityCoords(freeCamEntity)
end

function FreeCam.GetRotation()
    if not isFreeCamActive or not DoesEntityExist(freeCamEntity) then
        return GetEntityRotation(PlayerPedId(), 2)
    end
    return GetEntityRotation(freeCamEntity, 2)
end

-- Export pro ostatní moduly
exports('FreeCam', FreeCam)
return FreeCam
