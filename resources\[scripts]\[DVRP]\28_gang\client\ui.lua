local UI = {}
local isUIOpen = false
local currentMenu = nil

-- Import modulů
local Zones = exports[GetCurrentResourceName()]:Zones()

function UI.OpenMenu(menuType, data)
    if isUIOpen then return end
    
    isUIOpen = true
    currentMenu = menuType
    
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'openMenu',
        type = menuType,
        data = data or {}
    })
end

function UI.CloseMenu()
    if not isUIOpen then return end
    
    isUIOpen = false
    currentMenu = nil
    
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'closeMenu'
    })
end

function UI.UpdateMenu(data)
    if not isUIOpen then return end
    
    SendNUIMessage({
        action = 'updateMenu',
        data = data
    })
end

function UI.ShowTerritoryInfo()
    local currentTerritory = Zones.GetCurrentTerritory()
    if not currentTerritory then return end
    
    local territory = Zones.GetTerritoryById(currentTerritory)
    if not territory then return end
    
    local gangData = Config.Gangs[territory.gang]
    if not gangData then return end
    
    UI.OpenMenu('territoryInfo', {
        territory = territory,
        gang = gangData,
        territoryId = currentTerritory
    })
end

function UI.ShowGangStats()
    local QBCore = exports['qbx_core']:GetCoreObject()
    local PlayerData = QBCore.Functions.GetPlayerData()
    
    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return
    end
    
    TriggerServerEvent('28_gang:requestGangStats', PlayerData.gang.name)
end

function UI.ShowSprayMenu()
    local currentTerritory = Zones.GetCurrentTerritory()
    if not currentTerritory then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném teritoriu!')
        return
    end
    
    local territory = Zones.GetTerritoryById(currentTerritory)
    if not territory then return end
    
    TriggerServerEvent('28_gang:requestSprayInfo', currentTerritory)
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    UI.CloseMenu()
    cb('ok')
end)

RegisterNUICallback('sprayTerritory', function(data, cb)
    UI.CloseMenu()
    -- Spustit spray systém
    local Spray = exports[GetCurrentResourceName()]:Spray()
    if Spray then
        ExecuteCommand('gangspray')
    end
    cb('ok')
end)

RegisterNUICallback('defendTerritory', function(data, cb)
    UI.CloseMenu()
    -- Implementovat obranu teritoria (můžete rozšířit)
    TriggerEvent('28_gang:notification', 'info', 'Obrana teritoria zatím není implementována!')
    cb('ok')
end)

RegisterNUICallback('getTerritoryDetails', function(data, cb)
    local territoryId = data.territoryId
    TriggerServerEvent('28_gang:requestTerritoryDetails', territoryId)
    cb('ok')
end)

-- Event handlers
RegisterNetEvent('28_gang:gangStatsReceived', function(stats)
    UI.OpenMenu('gangStats', stats)
end)

RegisterNetEvent('28_gang:sprayInfoReceived', function(sprayInfo)
    UI.OpenMenu('sprayMenu', sprayInfo)
end)

RegisterNetEvent('28_gang:territoryDetailsReceived', function(details)
    UI.UpdateMenu(details)
end)

-- Commands pro UI
RegisterCommand('gangmenu', function()
    local QBCore = exports['qbx_core']:GetCoreObject()
    local PlayerData = QBCore.Functions.GetPlayerData()
    
    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return
    end
    
    UI.OpenMenu('mainMenu', {
        gang = PlayerData.gang,
        currentTerritory = Zones.GetCurrentTerritory()
    })
end, false)

RegisterCommand('territoryinfo', function()
    UI.ShowTerritoryInfo()
end, false)

RegisterCommand('gangstats', function()
    UI.ShowGangStats()
end, false)

-- Key mappings
RegisterKeyMapping('gangmenu', 'Gang Menu', 'keyboard', 'F6')
RegisterKeyMapping('territoryinfo', 'Territory Info', 'keyboard', 'F7')

-- Automatické zobrazení info při vstupu do teritoria
RegisterNetEvent('28_gang:playerEnteredTerritory', function(territoryId)
    if Config.UI.showTerritoryOnEnter then
        CreateThread(function()
            Wait(1000) -- Krátké zpoždění
            local territory = Zones.GetTerritoryById(territoryId)
            if territory and Config.Gangs[territory.gang] then
                local gangData = Config.Gangs[territory.gang]
                
                SendNUIMessage({
                    action = 'showTerritoryNotification',
                    data = {
                        gang = gangData,
                        territory = territory,
                        message = string.format('Vstoupil jsi do teritoria gangu %s', gangData.name)
                    }
                })
            end
        end)
    end
end)

-- HUD zobrazení
function UI.DrawHUD()
    local QBCore = exports['qbx_core']:GetCoreObject()
    local PlayerData = QBCore.Functions.GetPlayerData()
    
    if not PlayerData.gang or PlayerData.gang.name == 'none' then return end
    
    local currentTerritory = Zones.GetCurrentTerritory()
    if not currentTerritory then return end
    
    local territory = Zones.GetTerritoryById(currentTerritory)
    if not territory then return end
    
    local gangData = Config.Gangs[territory.gang]
    if not gangData then return end
    
    -- Zobrazit HUD info
    local hudText = string.format('Teritorium: %s', gangData.name)
    if territory.gang ~= PlayerData.gang.name then
        hudText = hudText .. ' (Nepřátelské)'
    end
    
    SetTextFont(Config.UI.textFont)
    SetTextScale(0.3, 0.3)
    SetTextColour(255, 255, 255, 255)
    SetTextOutline()
    SetTextEntry('STRING')
    AddTextComponentString(hudText)
    DrawText(0.02, 0.02)
end

-- Main thread pro HUD
if Config.UI.showHUD then
    CreateThread(function()
        while true do
            UI.DrawHUD()
            Wait(0)
        end
    end)
end

-- Export pro ostatní moduly
exports('UI', UI)
return UI
