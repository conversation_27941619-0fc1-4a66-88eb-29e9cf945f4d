-- Utility funkce
function NotifyPlayer(source, type, message)
    TriggerClientEvent('28_gang:notification', source, type, message)
end

function HasPermission(source, permission)
    if permission == 'admin' then
        return exports.qbx_core:HasPermission(source, 'admin')
    end
    return true
end

function GetPlayerName(source)
    local Player = exports.qbx_core:GetPlayer(source)
    if Player then
        local charinfo = json.decode(Player.PlayerData.charinfo)
        return charinfo.firstname .. ' ' .. charinfo.lastname
    end
    return 'Neznámý'
end

function CreateQBGang(gangName, gangLabel)
    if not Config.GangManagement.autoCreateQBGang then return true end
    
    -- Vytvořit QB gang
    local gangData = {
        name = gangName,
        label = gangLabel,
        grades = {
            [0] = { name = 'member', label = 'Člen' },
            [1] = { name = 'sergeant', label = 'Seržant' },
            [2] = { name = 'lieutenant', label = 'Poručík' },
            [3] = { name = 'leader', label = 'Vůdce' }
        }
    }
    
    -- <PERSON><PERSON> byste mohli přidat logiku pro vytvoření QB gangu
    -- Například uložení do shared/gangs.lua nebo databáze
    
    return true
end

function UpdatePlayerQBGang(source, gangName, rank)
    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return false end

    local gangGrade = 0
    if rank == 'sergeant' then gangGrade = 1
    elseif rank == 'lieutenant' then gangGrade = 2
    elseif rank == 'leader' then gangGrade = 3
    end

    Player.Functions.SetGang(gangName, gangGrade)
    return true
end

-- Gang Management Commands
RegisterCommand('gang_create', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local gangName = args[1]
    local gangLabel = table.concat(args, ' ', 2)
    
    if not gangName or not gangLabel then
        local usage = 'Použití: /gang_create [gang_name] [gang_label]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
        return
    end
    
    -- Kontrola, zda gang už existuje
    local Database = exports[GetCurrentResourceName()]:Database
    local existingGang = Database.GetGangInfo(gangName)
    if existingGang then
        local message = string.format(Config.Texts[Config.Locale]['gang_already_exists'], gangName)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Vytvořit gang
    local success = Database.CreateGang(gangName, gangLabel)
    
    if success then
        CreateQBGang(gangName, gangLabel)
        
        local message = string.format(Config.Texts[Config.Locale]['gang_created'], gangName)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end
        
        if Config.Debug then
            print(string.format('[28_gang] Gang created: %s (%s)', gangName, gangLabel))
        end
    else
        local message = 'Chyba při vytváření gangu!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
    end
end, false)

RegisterCommand('gang_delete', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local gangName = args[1]
    
    if not gangName then
        local usage = 'Použití: /gang_delete [gang_name]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
        return
    end
    
    -- Kontrola, zda gang existuje
    local Database = exports[GetCurrentResourceName()]:Database
    local gangInfo = Database.GetGangInfo(gangName)
    if not gangInfo then
        local message = string.format(Config.Texts[Config.Locale]['gang_not_found'], gangName)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end
    
    -- Smazat gang
    local success = Database.DeleteGang(gangName)
    
    if success then
        -- Aktualizovat všechny online hráče z gangu
        local Players = exports.qbx_core:GetQBPlayers()
        for _, playerId in pairs(Players) do
            local Player = exports.qbx_core:GetPlayer(playerId)
            if Player and Player.PlayerData.gang and Player.PlayerData.gang.name == gangName then
                Player.Functions.SetGang('none', 0)
                NotifyPlayer(playerId, 'info', string.format(Config.Texts[Config.Locale]['you_left_gang'], gangName))
            end
        end
        
        local message = string.format(Config.Texts[Config.Locale]['gang_deleted'], gangName)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end
        
        if Config.Debug then
            print(string.format('[28_gang] Gang deleted: %s', gangName))
        end
    else
        local message = 'Chyba při mazání gangu!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
    end
end, false)

RegisterCommand('gang_add', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local targetId = tonumber(args[1])
    local gangName = args[2]
    local rank = args[3] or Config.GangManagement.defaultRank
    
    if not targetId or not gangName then
        local usage = 'Použití: /gang_add [player_id] [gang_name] [rank]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
        return
    end
    
    local Player = exports.qbx_core:GetPlayer(targetId)
    if not Player then
        local message = Config.Texts[Config.Locale]['player_not_found']
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end
    
    -- Kontrola, zda gang existuje
    local Database = exports[GetCurrentResourceName()]:Database
    local gangInfo = Database.GetGangInfo(gangName)
    if not gangInfo then
        local message = string.format(Config.Texts[Config.Locale]['gang_not_found'], gangName)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end
    
    -- Kontrola hodnosti
    if not Config.GangRanks[rank] then
        local availableRanks = {}
        for rankName in pairs(Config.GangRanks) do
            table.insert(availableRanks, rankName)
        end
        local message = string.format(Config.Texts[Config.Locale]['invalid_rank'], table.concat(availableRanks, ', '))
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end
    
    -- Kontrola, zda gang není plný
    local memberCount = Database.GetGangMemberCount(gangName)
    if memberCount >= gangInfo.max_members then
        local message = string.format(Config.Texts[Config.Locale]['gang_is_full'], gangName, gangInfo.max_members)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Kontrola, zda hráč už není v gangu
    local currentGang = Database.GetPlayerGangInfo(Player.PlayerData.citizenid)
    if currentGang then
        local message = string.format(Config.Texts[Config.Locale]['player_already_in_gang'], GetPlayerName(targetId), currentGang.gang)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Přidat hráče do gangu
    local success = Database.AddPlayerToGang(Player.PlayerData.citizenid, gangName, rank)
    
    if success then
        -- Aktualizovat QB gang
        UpdatePlayerQBGang(targetId, gangName, rank)
        
        local playerName = GetPlayerName(targetId)
        local rankLabel = Config.GangRanks[rank].name
        
        local message = string.format(Config.Texts[Config.Locale]['player_added_to_gang'], playerName, gangName, rankLabel)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end
        
        -- Notifikovat hráče
        NotifyPlayer(targetId, 'success', string.format(Config.Texts[Config.Locale]['you_joined_gang'], gangName, rankLabel))
        
        if Config.Debug then
            print(string.format('[28_gang] Player %s added to gang %s as %s', playerName, gangName, rank))
        end
    else
        local message = 'Chyba při přidávání hráče do gangu!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
    end
end, false)

RegisterCommand('gang_remove', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end

    local targetId = tonumber(args[1])

    if not targetId then
        local usage = 'Použití: /gang_remove [player_id]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
        return
    end

    local Player = exports.qbx_core:GetPlayer(targetId)
    if not Player then
        local message = Config.Texts[Config.Locale]['player_not_found']
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Získat aktuální gang hráče
    local Database = exports[GetCurrentResourceName()]:Database
    local currentGang = Database.GetPlayerGangInfo(Player.PlayerData.citizenid)
    if not currentGang then
        local message = 'Hráč není v žádném gangu!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Odebrat hráče z gangu
    local success = Database.RemovePlayerFromGang(Player.PlayerData.citizenid)

    if success then
        -- Aktualizovat QB gang
        Player.Functions.SetGang('none', 0)

        local playerName = GetPlayerName(targetId)
        local message = string.format(Config.Texts[Config.Locale]['player_removed_from_gang'], playerName, currentGang.gang)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end

        -- Notifikovat hráče
        NotifyPlayer(targetId, 'info', string.format(Config.Texts[Config.Locale]['you_left_gang'], currentGang.gang))

        if Config.Debug then
            print(string.format('[28_gang] Player %s removed from gang %s', playerName, currentGang.gang))
        end
    else
        local message = 'Chyba při odebírání hráče z gangu!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
    end
end, false)

RegisterCommand('gang_rank', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end

    local targetId = tonumber(args[1])
    local newRank = args[2]

    if not targetId or not newRank then
        local usage = 'Použití: /gang_rank [player_id] [rank]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
        return
    end

    local Player = exports.qbx_core:GetPlayer(targetId)
    if not Player then
        local message = Config.Texts[Config.Locale]['player_not_found']
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Kontrola hodnosti
    if not Config.GangRanks[newRank] then
        local availableRanks = {}
        for rankName in pairs(Config.GangRanks) do
            table.insert(availableRanks, rankName)
        end
        local message = string.format(Config.Texts[Config.Locale]['invalid_rank'], table.concat(availableRanks, ', '))
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Získat aktuální gang hráče
    local Database = exports[GetCurrentResourceName()]:Database
    local currentGang = Database.GetPlayerGangInfo(Player.PlayerData.citizenid)
    if not currentGang then
        local message = 'Hráč není v žádném gangu!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    -- Aktualizovat hodnost
    local success = Database.UpdatePlayerRank(Player.PlayerData.citizenid, newRank)

    if success then
        -- Aktualizovat QB gang
        UpdatePlayerQBGang(targetId, currentGang.gang, newRank)

        local playerName = GetPlayerName(targetId)
        local rankLabel = Config.GangRanks[newRank].name

        local message = string.format(Config.Texts[Config.Locale]['rank_updated'], playerName, rankLabel)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end

        -- Notifikovat hráče
        if Config.GangRanks[newRank].level > Config.GangRanks[currentGang.rank].level then
            NotifyPlayer(targetId, 'success', string.format(Config.Texts[Config.Locale]['you_promoted'], rankLabel))
        else
            NotifyPlayer(targetId, 'info', string.format(Config.Texts[Config.Locale]['you_demoted'], rankLabel))
        end

        if Config.Debug then
            print(string.format('[28_gang] Player %s rank updated to %s in gang %s', playerName, newRank, currentGang.gang))
        end
    else
        local message = 'Chyba při aktualizaci hodnosti!'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
    end
end, false)

RegisterCommand('gang_info', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end

    local gangName = args[1]

    if not gangName then
        local usage = 'Použití: /gang_info [gang_name]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
        return
    end

    local Database = exports[GetCurrentResourceName()]:Database
    local gangInfo = Database.GetGangInfo(gangName)
    if not gangInfo then
        local message = string.format(Config.Texts[Config.Locale]['gang_not_found'], gangName)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end

    local members = Database.GetGangMembers(gangName)
    local memberCount = #members

    local leaderName = 'Žádný'
    for _, member in pairs(members) do
        if member.rank == 'leader' then
            if member.charinfo then
                local charinfo = json.decode(member.charinfo)
                leaderName = charinfo.firstname .. ' ' .. charinfo.lastname
            else
                leaderName = member.citizenid
            end
            break
        end
    end

    local message = string.format('^3=== GANG INFO: %s ===^7\n', gangName:upper())
    message = message .. string.format('^2Label:^7 %s\n', gangInfo.label)
    message = message .. string.format('^2Členů:^7 %d/%d\n', memberCount, gangInfo.max_members)
    message = message .. string.format('^2Vůdce:^7 %s\n', leaderName)
    message = message .. string.format('^2Reputační požadavek:^7 %d\n', gangInfo.reputation_requirement)
    message = message .. string.format('^2Typ teritoria:^7 %s\n', gangInfo.territory_type)
    message = message .. '^3=== ČLENOVÉ ===^7\n'

    for _, member in pairs(members) do
        local memberName = member.citizenid
        if member.charinfo then
            local charinfo = json.decode(member.charinfo)
            memberName = charinfo.firstname .. ' ' .. charinfo.lastname
        end

        local rankLabel = Config.GangRanks[member.rank] and Config.GangRanks[member.rank].name or member.rank
        message = message .. string.format('^4%s^7 - ^5%s^7\n', memberName, rankLabel)
    end

    if source == 0 then
        print(message)
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 255},
            multiline = true,
            args = {"Gang System", message}
        })
    end
end, false)

RegisterCommand('gang_list', function(source, args, rawCommand)
    if source ~= 0 and not HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end

    local Database = exports[GetCurrentResourceName()]:Database
    local gangs = Database.GetAllGangs()

    local message = '^3=== VŠECHNY GANGY ===^7\n'

    if #gangs > 0 then
        for _, gang in pairs(gangs) do
            local memberCount = Database.GetGangMemberCount(gang.name)
            message = message .. string.format('^2%s^7 (%s) - ^4%d/%d členů^7 - ^5Req: %d rep^7\n',
                gang.name, gang.label, memberCount, gang.max_members, gang.reputation_requirement)
        end
    else
        message = message .. '^1Žádné gangy nenalezeny^7'
    end

    if source == 0 then
        print(message)
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 255},
            multiline = true,
            args = {"Gang System", message}
        })
    end
end, false)

-- Event handlers
RegisterNetEvent('28_gang:requestPlayerGangInfo', function()
    local source = source
    local Player = exports.qbx_core:GetPlayer(source)

    if not Player then return end

    local Database = exports[GetCurrentResourceName()]:Database
    local gangInfo = Database.GetPlayerGangInfo(Player.PlayerData.citizenid)
    TriggerClientEvent('28_gang:playerGangInfoReceived', source, gangInfo)
end)

-- Inicializace - vytvoření základních gangů z configu
CreateThread(function()
    Wait(5000) -- Počkat na inicializaci databáze

    local Database = exports[GetCurrentResourceName()]:Database

    for gangName, gangData in pairs(Config.Gangs) do
        local existingGang = Database.GetGangInfo(gangName)

        if not existingGang then
            local success = Database.CreateGang(
                gangName,
                gangData.label or gangData.name,
                nil, -- žádný leader
                gangData.maxMembers or Config.GangManagement.maxMembers,
                gangData.reputationRequirement or Config.Reputation.requiredForTakeover,
                'default'
            )

            if success and Config.Debug then
                print(string.format('[28_gang] Auto-created gang: %s (%s)', gangName, gangData.label or gangData.name))
            end
        end
    end
end)

-- Export funkcí
exports('GetPlayerGangInfo', function(citizenid)
    local Database = exports[GetCurrentResourceName()]:Database
    return Database.GetPlayerGangInfo(citizenid)
end)

exports('GetGangInfo', function(gangName)
    local Database = exports[GetCurrentResourceName()]:Database
    return Database.GetGangInfo(gangName)
end)

exports('GetGangMembers', function(gangName)
    local Database = exports[GetCurrentResourceName()]:Database
    return Database.GetGangMembers(gangName)
end)

exports('IsPlayerInGang', function(citizenid, gangName)
    local Database = exports[GetCurrentResourceName()]:Database
    local gangInfo = Database.GetPlayerGangInfo(citizenid)
    return gangInfo and gangInfo.gang == gangName
end)

exports('HasGangPermission', function(citizenid, permission)
    local Database = exports[GetCurrentResourceName()]:Database
    local gangInfo = Database.GetPlayerGangInfo(citizenid)
    if not gangInfo or not Config.GangRanks[gangInfo.rank] then return false end

    local permissions = Config.GangRanks[gangInfo.rank].permissions
    if not permissions then return false end

    for _, perm in pairs(permissions) do
        if perm == permission then
            return true
        end
    end

    return false
end)
