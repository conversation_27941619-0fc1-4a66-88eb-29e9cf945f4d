-- 28_gang Database Tables
-- Import this file into your MySQL database

-- Table for gang territories
CREATE TABLE IF NOT EXISTS `gang_territories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `gang` varchar(50) NOT NULL DEFAULT 'none',
    `coords` longtext NOT NULL,
    `radius` float NOT NULL DEFAULT 50.0,
    `territory_type` varchar(50) NOT NULL DEFAULT 'default',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `gang` (`gang`),
    KEY `territory_type` (`territory_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for gang sprays
CREATE TABLE IF NOT EXISTS `gang_sprays` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `territory_id` int(11) NOT NULL,
    `gang` varchar(50) NOT NULL,
    `coords` longtext NOT NULL,
    `created_by` varchar(50) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `territory_id` (`territory_id`),
    KEY `gang` (`gang`),
    KEY `created_by` (`created_by`),
    CONSTRAINT `gang_sprays_ibfk_1` FOREIGN KEY (`territory_id`) REFERENCES `gang_territories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for gang reputation
CREATE TABLE IF NOT EXISTS `gang_reputation` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `gang` varchar(50) NOT NULL,
    `reputation` int(11) NOT NULL DEFAULT 0,
    `total_sprays` int(11) NOT NULL DEFAULT 0,
    `total_drug_sales` int(11) NOT NULL DEFAULT 0,
    `territories_taken` int(11) NOT NULL DEFAULT 0,
    `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `citizenid_gang` (`citizenid`, `gang`),
    KEY `citizenid` (`citizenid`),
    KEY `gang` (`gang`),
    KEY `reputation` (`reputation`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for gang members
CREATE TABLE IF NOT EXISTS `gang_members` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `gang` varchar(50) NOT NULL,
    `rank` varchar(50) NOT NULL DEFAULT 'member',
    `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `citizenid` (`citizenid`),
    KEY `gang` (`gang`),
    KEY `rank` (`rank`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for gang information
CREATE TABLE IF NOT EXISTS `gang_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `label` varchar(100) NOT NULL,
    `leader` varchar(50) DEFAULT NULL,
    `max_members` int(11) NOT NULL DEFAULT 20,
    `reputation_requirement` int(11) NOT NULL DEFAULT 100,
    `territory_type` varchar(50) NOT NULL DEFAULT 'default',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`),
    KEY `leader` (`leader`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default gangs from config
INSERT IGNORE INTO `gang_info` (`name`, `label`, `max_members`, `reputation_requirement`) VALUES
('ballas', 'The Ballas', 20, 100),
('grove', 'Grove Street Families', 20, 100),
('vagos', 'Los Santos Vagos', 20, 100),
('families', 'The Families', 20, 100);

-- Example territory (optional - remove if not needed)
-- INSERT INTO `gang_territories` (`gang`, `coords`, `radius`, `territory_type`) VALUES
-- ('none', '{"x": -100.0, "y": -100.0, "z": 20.0}', 75.0, 'default');
