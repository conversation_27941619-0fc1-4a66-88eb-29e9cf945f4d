-- Globální proměnné
local sprays = {}

-- Utility funkce
function GetPlayerGang(source)
    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return nil end

    return Player.PlayerData.gang and Player.PlayerData.gang.name ~= 'none' and Player.PlayerData.gang.name or nil
end

function NotifyPlayer(source, type, message)
    TriggerClientEvent('28_gang:notification', source, type, message)
end

function NotifyGangMembers(gang, type, message)
    local Players = exports.qbx_core:GetQBPlayers()
    for _, playerId in pairs(Players) do
        local Player = exports.qbx_core:GetPlayer(playerId)
        if Player and Player.PlayerData.gang and Player.PlayerData.gang.name == gang then
            TriggerClientEvent('28_gang:notification', playerId, type, message)
        end
    end
end

function CheckTerritoryTakeover(territoryId)
    local territories = exports[GetCurrentResourceName()]:GetTerritories()
    local Database = exports[GetCurrentResourceName()]:Database

    if not territories[territoryId] then return end

    local territory = territories[territoryId]
    local sprayStats = Database.GetTerritoryStats(territoryId)

    -- Najít gang s nejvíce spreje
    local maxSprays = 0
    local leadingGang = nil

    for gang, count in pairs(sprayStats) do
        if count > maxSprays then
            maxSprays = count
            leadingGang = gang
        end
    end

    -- Kontrola, zda má gang dostatek sprejů pro převzetí
    if leadingGang and maxSprays >= Config.Spray.requiredSprays and leadingGang ~= territory.gang then
        -- Kontrola reputace (pokud je povolena)
        if Config.Reputation.enabled then
            -- Získat reputační požadavek pro tento gang/teritorium
            local requiredReputation = Database.GetGangReputationRequirement(leadingGang)

            local gangReputation = Database.GetGangReputationLeaderboard(leadingGang, 1)
            local totalReputation = 0

            if gangReputation and gangReputation[1] then
                totalReputation = gangReputation[1].reputation or 0
            end

            if totalReputation < requiredReputation then
                if Config.Debug then
                    print(string.format('[28_gang] Gang %s has insufficient reputation for takeover: %d/%d',
                        leadingGang, totalReputation, requiredReputation))
                end
                return false
            end
        end

        -- Převzít teritorium
        Database.UpdateTerritoryGang(territoryId, leadingGang)
        territory.gang = leadingGang

        -- Aktualizovat klienty
        TriggerClientEvent('28_gang:territoryUpdated', -1, territoryId, territory)
        TriggerClientEvent('28_gang:territoryTaken', -1, territoryId, leadingGang, territory.gang)

        -- Trigger reputační event
        TriggerEvent('28_gang:territoryTaken', territoryId, leadingGang)

        -- Notifikace
        if Config.Gangs[leadingGang] then
            local gangName = Config.Gangs[leadingGang].name
            NotifyGangMembers(leadingGang, 'success', string.format(Config.Texts[Config.Locale]['territory_taken'], gangName))
        end

        if Config.Debug then
            print('[28_gang] Territory ' .. territoryId .. ' taken by ' .. leadingGang)
        end

        return true
    end

    return false
end

-- Event handlers
RegisterNetEvent('28_gang:createSpray', function(coords, gang, territoryId)
    local source = source
    local Player = exports.qbx_core:GetPlayer(source)

    if not Player then return end

    local playerGang = GetPlayerGang(source)
    if not playerGang then
        NotifyPlayer(source, 'error', 'Nejsi v žádném gangu!')
        return
    end

    if playerGang ~= gang then
        NotifyPlayer(source, 'error', 'Neplatný gang!')
        return
    end

    -- Kontrola spray itemu na serveru
    local hasItem = exports.ox_inventory:GetItemCount(source, Config.Spray.sprayItem)
    if hasItem < 1 then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['spray_no_item'])
        return
    end
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Neplatné teritorium!')
        return
    end
    
    -- Kontrola, zda už hráč nemá spray na této pozici
    for _, spray in pairs(sprays) do
        if spray.coords and spray.territory_id == territoryId then
            local distance = #(vector3(coords.x, coords.y, coords.z) - vector3(spray.coords.x, spray.coords.y, spray.coords.z))
            if distance < Config.Spray.sprayRadius then
                NotifyPlayer(source, 'error', 'Zde už je spray!')
                return
            end
        end
    end
    
    -- Kontrola maximálního počtu sprejů na teritorium
    local Database = exports[GetCurrentResourceName()]:Database
    local sprayCount = Database.CountSpraysByGangInTerritory(territoryId, gang)
    if sprayCount >= Config.Spray.maxSpraysPerTerritory then
        NotifyPlayer(source, 'error', 'Dosažen maximální počet sprejů na tomto teritoriu!')
        return
    end

    -- Vytvořit spray
    local sprayId = Database.CreateSpray(territoryId, gang, coords, Player.PlayerData.citizenid)
    
    if sprayId then
        sprays[sprayId] = {
            id = sprayId,
            territory_id = territoryId,
            gang = gang,
            coords = coords,
            created_by = Player.PlayerData.citizenid
        }
        
        TriggerClientEvent('28_gang:sprayCreated', -1, sprayId, sprays[sprayId])

        -- Trigger reputační event
        TriggerEvent('28_gang:sprayCompleted', territoryId)

        -- Kontrola převzetí teritoria
        CreateThread(function()
            Wait(1000) -- Krátké zpoždění pro aktualizaci databáze
            CheckTerritoryTakeover(territoryId)
        end)
        
        if Config.Debug then
            print('[28_gang] Spray created by ' .. Player.PlayerData.name .. ' (' .. gang .. ') in territory ' .. territoryId)
        end
    else
        NotifyPlayer(source, 'error', 'Chyba při vytváření spreje!')
    end
end)

RegisterNetEvent('28_gang:requestSprayInfo', function(territoryId)
    local source = source
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Neplatné teritorium!')
        return
    end
    
    local territory = territories[territoryId]
    local Database = exports[GetCurrentResourceName()]:Database
    local sprayStats = Database.GetTerritoryStats(territoryId)
    local territorySprays = Database.GetSpraysByTerritory(territoryId)
    
    local sprayInfo = {
        territory = territory,
        stats = sprayStats,
        sprays = territorySprays,
        requiredSprays = Config.Spray.requiredSprays,
        maxSprays = Config.Spray.maxSpraysPerTerritory
    }
    
    TriggerClientEvent('28_gang:sprayInfoReceived', source, sprayInfo)
end)

RegisterNetEvent('28_gang:requestGangStats', function(gang)
    local source = source
    
    local playerGang = GetPlayerGang(source)
    if not playerGang or playerGang ~= gang then
        NotifyPlayer(source, 'error', 'Neplatný gang!')
        return
    end
    
    local Database = exports[GetCurrentResourceName()]:Database
    local stats = Database.GetGangStats(gang)
    
    -- Přidat další statistiky
    local gangTerritories = {}
    for id, territory in pairs(territories) do
        if territory.gang == gang then
            gangTerritories[id] = territory
        end
    end
    
    local gangSprays = {}
    for id, spray in pairs(sprays) do
        if spray.gang == gang then
            gangSprays[id] = spray
        end
    end
    
    local gangStats = {
        gang = gang,
        gangData = Config.Gangs[gang],
        territories = gangTerritories,
        sprays = gangSprays,
        stats = stats
    }
    
    TriggerClientEvent('28_gang:gangStatsReceived', source, gangStats)
end)

RegisterNetEvent('28_gang:requestTerritoryDetails', function(territoryId)
    local source = source
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Neplatné teritorium!')
        return
    end
    
    local territory = territories[territoryId]
    local Database = exports[GetCurrentResourceName()]:Database
    local sprayStats = Database.GetTerritoryStats(territoryId)
    local territoryDetails = {
        territory = territory,
        stats = sprayStats,
        gangs = Config.Gangs
    }
    
    TriggerClientEvent('28_gang:territoryDetailsReceived', source, territoryDetails)
end)

-- Admin commands
RegisterCommand('gang_clearsprays', function(source, args, rawCommand)
    if source ~= 0 and not exports.qbx_core:HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local territoryId = tonumber(args[1])
    
    if territoryId then
        -- Smazat spreje z konkrétního teritoria
        local Database = exports[GetCurrentResourceName()]:Database
        Database.DeleteSpraysByTerritory(territoryId)
        
        for sprayId, spray in pairs(sprays) do
            if spray.territory_id == territoryId then
                sprays[sprayId] = nil
                TriggerClientEvent('28_gang:sprayDeleted', -1, sprayId)
            end
        end
        
        local message = 'Spreje smazány z teritoria ' .. territoryId
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end
    else
        -- Smazat všechny spreje
        local Database = exports[GetCurrentResourceName()]:Database
        Database.CleanupOldSprays(0) -- Smaže všechny spreje
        
        for sprayId in pairs(sprays) do
            TriggerClientEvent('28_gang:sprayDeleted', -1, sprayId)
        end
        sprays = {}
        
        local message = 'Všechny spreje smazány'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'success', message)
        end
    end
end, false)

-- Automatické čištění starých sprejů
CreateThread(function()
    while true do
        Wait(3600000) -- 1 hodina
        
        if Config.Spray.autoCleanup then
            local cleanupDays = Config.Spray.cleanupDays or 7
            local Database = exports[GetCurrentResourceName()]:Database
            Database.CleanupOldSprays(cleanupDays)

            -- Reload sprejů z databáze
            sprays = Database.GetAllSprays()
            TriggerClientEvent('28_gang:spraysLoaded', -1, sprays)
            
            if Config.Debug then
                print('[28_gang] Old sprays cleaned up (older than ' .. cleanupDays .. ' days)')
            end
        end
    end
end)

-- Event pro odebrání spray itemu
RegisterNetEvent('28_gang:removeSprayItem', function()
    local source = source
    local Player = exports.qbx_core:GetPlayer(source)

    if Player then
        exports.ox_inventory:RemoveItem(source, Config.Spray.sprayItem, 1)
    end
end)

-- Export funkcí
exports('GetSprays', function()
    return sprays
end)

exports('GetSpraysByTerritory', function(territoryId)
    local territorySprays = {}
    for id, spray in pairs(sprays) do
        if spray.territory_id == territoryId then
            territorySprays[id] = spray
        end
    end
    return territorySprays
end)
