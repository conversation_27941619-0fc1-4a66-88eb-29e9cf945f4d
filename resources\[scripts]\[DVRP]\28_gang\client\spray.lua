local Spray = {}
-- QBX Core je dostupný přes exports.qbx_core
local sprays = {}
local isSprayingActive = false
local sprayingStartTime = 0
local lastSprayTime = 0

-- Import modulů
local Zones = exports[GetCurrentResourceName()]:Zones()

function Spray.LoadSprays(data)
    sprays = data or {}
    Spray.CreateBlips()
end

function Spray.CreateBlips()
    -- Smazat existující spray blipy
    for _, spray in pairs(sprays) do
        if spray.blip then
            RemoveBlip(spray.blip)
        end
    end
    
    -- Vytvořit nové spray blipy
    if Config.Blips.showSprays then
        for _, spray in pairs(sprays) do
            if spray.coords and Config.Gangs[spray.gang] then
                local gangData = Config.Gangs[spray.gang]
                local blip = AddBlipForCoord(spray.coords.x, spray.coords.y, spray.coords.z)
                
                SetBlipSprite(blip, Config.Blips.sprayBlip.sprite)
                SetBlipColour(blip, gangData.blip.color)
                SetBlipScale(blip, Config.Blips.sprayBlip.scale)
                SetBlipAsShortRange(blip, Config.Blips.sprayBlip.shortRange)
                BeginTextCommandSetBlipName('STRING')
                AddTextComponentString(gangData.name .. ' Spray')
                EndTextCommandSetBlipName(blip)
                
                spray.blip = blip
            end
        end
    end
end

function Spray.CanSpray()
    local PlayerData = exports.qbx_core.PlayerData
    
    -- Kontrola gangu
    if not PlayerData.gang or not PlayerData.gang.name or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return false
    end
    
    -- Kontrola itemu
    local hasItem = exports.ox_inventory:Search('count', Config.Spray.sprayItem) or 0
    if hasItem < 1 then
        TriggerEvent('28_gang:notification', 'error', Config.Texts[Config.Locale]['spray_no_item'])
        return false
    end
    
    -- Kontrola cooldownu
    local currentTime = GetGameTimer()
    if currentTime - lastSprayTime < Config.Spray.cooldownTime then
        local remainingTime = math.ceil((Config.Spray.cooldownTime - (currentTime - lastSprayTime)) / 1000)
        TriggerEvent('28_gang:notification', 'error', string.format(Config.Texts[Config.Locale]['spray_cooldown'], remainingTime))
        return false
    end
    
    -- Kontrola teritoria
    local currentTerritory = Zones.GetCurrentTerritory()
    if not currentTerritory then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném teritoriu!')
        return false
    end
    
    local territory = Zones.GetTerritoryById(currentTerritory)
    if territory.gang == PlayerData.gang.name then
        TriggerEvent('28_gang:notification', 'error', 'Toto teritorium už patří tvému gangu!')
        return false
    end
    
    return true
end

function Spray.StartSpray()
    if isSprayingActive or not Spray.CanSpray() then return end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local currentTerritory = Zones.GetCurrentTerritory()
    
    -- Kontrola, zda už není na této pozici spray
    for _, spray in pairs(sprays) do
        if spray.coords then
            local distance = #(playerCoords - spray.coords)
            if distance < Config.Spray.sprayRadius then
                TriggerEvent('28_gang:notification', 'error', 'Zde už je spray!')
                return
            end
        end
    end
    
    isSprayingActive = true
    sprayingStartTime = GetGameTimer()
    
    -- Spustit animaci
    local animDict = Config.Animations.spray.dict
    local animName = Config.Animations.spray.anim
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(100)
    end
    
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, -1, Config.Animations.spray.flag, 0, false, false, false)
    
    -- Zmrazit hráče
    FreezeEntityPosition(playerPed, true)
    
    TriggerEvent('28_gang:notification', 'info', Config.Texts[Config.Locale]['spray_start'])
    
    -- Progress bar
    CreateThread(function()
        while isSprayingActive do
            local currentTime = GetGameTimer()
            local progress = (currentTime - sprayingStartTime) / Config.Spray.sprayTime
            
            if progress >= 1.0 then
                Spray.CompleteSpray()
                break
            end
            
            -- Zobrazit progress
            local progressText = string.format('Sprejuješ... %.0f%%', progress * 100)
            SetTextFont(Config.UI.textFont)
            SetTextScale(Config.UI.textScale, Config.UI.textScale)
            SetTextColour(255, 255, 255, 255)
            SetTextOutline()
            SetTextEntry('STRING')
            AddTextComponentString(progressText)
            DrawText(0.5, 0.5)
            
            -- Kontrola přerušení
            if IsControlJustPressed(0, 73) or -- X key
               IsPedInAnyVehicle(playerPed, false) or
               IsPedDeadOrDying(playerPed, false) then
                Spray.CancelSpray()
                break
            end
            
            Wait(0)
        end
    end)
end

function Spray.CompleteSpray()
    if not isSprayingActive then return end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local PlayerData = exports.qbx_core.PlayerData
    local currentTerritory = Zones.GetCurrentTerritory()
    
    -- Ukončit animaci
    ClearPedTasks(playerPed)
    FreezeEntityPosition(playerPed, false)
    
    isSprayingActive = false
    lastSprayTime = GetGameTimer()
    
    -- Odebrat item
    TriggerServerEvent('28_gang:removeSprayItem')
    
    -- Poslat na server
    TriggerServerEvent('28_gang:createSpray', playerCoords, PlayerData.gang.name, currentTerritory)
    
    TriggerEvent('28_gang:notification', 'success', Config.Texts[Config.Locale]['spray_complete'])
end

function Spray.CancelSpray()
    if not isSprayingActive then return end
    
    local playerPed = PlayerPedId()
    
    -- Ukončit animaci
    ClearPedTasks(playerPed)
    FreezeEntityPosition(playerPed, false)
    
    isSprayingActive = false
    
    TriggerEvent('28_gang:notification', 'error', Config.Texts[Config.Locale]['spray_cancelled'])
end

function Spray.DrawSprays()
    local playerCoords = GetEntityCoords(PlayerPedId())
    
    for _, spray in pairs(sprays) do
        if spray.coords then
            local distance = #(playerCoords - spray.coords)
            
            if distance < Config.UI.maxViewDistance then
                local gangData = Config.Gangs[spray.gang]
                if gangData then
                    -- Vykreslit spray marker
                    DrawMarker(1, spray.coords.x, spray.coords.y, spray.coords.z - 1.0, 
                              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                              2.0, 2.0, 0.5, 
                              gangData.sprayColor.r, gangData.sprayColor.g, gangData.sprayColor.b, 200, 
                              false, true, 2, false, nil, nil, false)
                    
                    -- Zobrazit text
                    if Config.UI.showDistance and distance < 10.0 then
                        local screenX, screenY = GetScreenCoordFromWorldCoord(spray.coords.x, spray.coords.y, spray.coords.z + 1.0)
                        if screenX and screenY then
                            SetTextFont(Config.UI.textFont)
                            SetTextScale(Config.UI.textScale, Config.UI.textScale)
                            SetTextColour(gangData.sprayColor.r, gangData.sprayColor.g, gangData.sprayColor.b, 255)
                            SetTextOutline()
                            SetTextEntry('STRING')
                            AddTextComponentString(gangData.name)
                            DrawText(screenX, screenY)
                        end
                    end
                end
            end
        end
    end
end

-- Event handlers
RegisterNetEvent('28_gang:spraysLoaded', function(data)
    Spray.LoadSprays(data)
end)

RegisterNetEvent('28_gang:sprayCreated', function(id, data)
    sprays[id] = data
    Spray.CreateBlips()
end)

RegisterNetEvent('28_gang:sprayDeleted', function(id)
    if sprays[id] then
        if sprays[id].blip then
            RemoveBlip(sprays[id].blip)
        end
        sprays[id] = nil
    end
end)

RegisterNetEvent('28_gang:territoryTaken', function(territoryId, newGang, oldGang)
    local newGangData = Config.Gangs[newGang]
    local oldGangData = Config.Gangs[oldGang]
    
    if newGangData and oldGangData then
        TriggerEvent('28_gang:notification', 'success', 
            string.format(Config.Texts[Config.Locale]['territory_taken'], newGangData.name))
    end
end)

-- Key mapping pro sprejování
RegisterKeyMapping('gangspray', 'Gang Spray', 'keyboard', 'E')
RegisterCommand('gangspray', function()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local currentTerritory = Zones.GetCurrentTerritory()
    
    if currentTerritory then
        -- Kontrola vzdálenosti od stěny nebo objektu
        local hit, _, _, _, _ = GetShapeTestResult(StartShapeTestRay(playerCoords.x, playerCoords.y, playerCoords.z, 
                                                                    playerCoords.x, playerCoords.y - 2.0, playerCoords.z, 
                                                                    -1, PlayerPedId(), 0))
        if hit then
            Spray.StartSpray()
        else
            TriggerEvent('28_gang:notification', 'error', 'Musíš být blízko stěny!')
        end
    end
end, false)

-- Main thread pro vykreslování sprejů
CreateThread(function()
    while true do
        Spray.DrawSprays()
        Wait(0)
    end
end)

-- Export pro ostatní moduly
exports('Spray', Spray)
return Spray
