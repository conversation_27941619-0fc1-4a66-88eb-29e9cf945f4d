-- Utility funkce
function NotifyPlayer(source, type, message)
    TriggerClientEvent('28_gang:notification', source, type, message)
end

function GetPlayerGang(source)
    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return nil end

    return Player.PlayerData.gang and Player.PlayerData.gang.name ~= 'none' and Player.PlayerData.gang.name or nil
end

function IsPlayerInTerritory(source, territoryId)
    local playerTerritory = exports[GetCurrentResourceName()]:GetPlayerTerritory(source)
    return playerTerritory == territoryId
end

function GetTerritoryOwner(territoryId)
    if not territories[territoryId] then return nil end
    return territories[territoryId].gang
end

function GetGangTerritories(gang)
    local gangTerritories = {}
    for id, territory in pairs(territories) do
        if territory.gang == gang then
            gangTerritories[id] = territory
        end
    end
    return gangTerritories
end

function GetNearbyTerritories(coords, radius)
    local nearbyTerritories = {}
    
    for id, territory in pairs(territories) do
        if territory.coords then
            local distance = #(vector3(coords.x, coords.y, coords.z) - vector3(territory.coords.x, territory.coords.y, territory.coords.z))
            if distance <= radius then
                nearbyTerritories[id] = {
                    territory = territory,
                    distance = distance
                }
            end
        end
    end
    
    return nearbyTerritories
end

function ValidateTerritoryPosition(coords, radius, excludeId)
    -- Kontrola minimální vzdálenosti od ostatních teritorií
    for id, territory in pairs(territories) do
        if id ~= excludeId and territory.coords then
            local distance = #(vector3(coords.x, coords.y, coords.z) - vector3(territory.coords.x, territory.coords.y, territory.coords.z))
            local minDistance = Config.Territories.minDistance
            
            if distance < minDistance then
                return false, 'Teritorium je příliš blízko k jinému teritoriu! (Minimální vzdálenost: ' .. minDistance .. 'm)'
            end
        end
    end
    
    -- Kontrola, zda je teritorium v povolené oblasti (můžete rozšířit)
    if coords.z < -50 or coords.z > 1000 then
        return false, 'Neplatná výška teritoria!'
    end
    
    -- Kontrola poloměru
    if radius < Config.Territories.minRadius or radius > Config.Territories.maxRadius then
        return false, string.format('Neplatný poloměr! (Min: %s, Max: %s)', Config.Territories.minRadius, Config.Territories.maxRadius)
    end
    
    return true, 'OK'
end

-- Event handlers pro zone management
RegisterNetEvent('28_gang:assignTerritoryToGang', function(territoryId, gang)
    local source = source

    if not exports.qbx_core:HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Teritorium neexistuje!')
        return
    end
    
    if gang ~= 'none' and not Config.Gangs[gang] then
        NotifyPlayer(source, 'error', 'Neplatný gang!')
        return
    end
    
    -- Aktualizovat teritorium
    local Database = exports[GetCurrentResourceName()]:Database
    local success = Database.UpdateTerritoryGang(territoryId, gang)
    
    if success then
        local oldGang = territories[territoryId].gang
        territories[territoryId].gang = gang
        
        TriggerClientEvent('28_gang:territoryUpdated', -1, territoryId, territories[territoryId])
        
        local gangName = gang == 'none' and 'Žádný' or (Config.Gangs[gang] and Config.Gangs[gang].name or gang)
        NotifyPlayer(source, 'success', 'Teritorium přiřazeno gangu: ' .. gangName)
        
        if Config.Debug then
            print('[28_gang] Territory ' .. territoryId .. ' assigned to gang: ' .. gang .. ' (was: ' .. oldGang .. ')')
        end
    else
        NotifyPlayer(source, 'error', 'Chyba při přiřazování teritoria!')
    end
end)

RegisterNetEvent('28_gang:getTerritoryInfo', function(territoryId)
    local source = source
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Teritorium neexistuje!')
        return
    end
    
    local territory = territories[territoryId]
    local Database = exports[GetCurrentResourceName()]:Database
    local sprayCount = Database.CountSpraysByGangInTerritory(territoryId, territory.gang)
    
    local info = {
        territory = territory,
        sprayCount = sprayCount,
        gangData = Config.Gangs[territory.gang],
        isOwned = territory.gang ~= 'none'
    }
    
    TriggerClientEvent('28_gang:territoryInfoReceived', source, info)
end)

RegisterNetEvent('28_gang:requestNearbyTerritories', function(radius)
    local source = source
    local Player = exports.qbx_core:GetPlayer(source)

    if not Player then return end
    
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local searchRadius = radius or 500.0
    
    local nearbyTerritories = GetNearbyTerritories(playerCoords, searchRadius)
    
    TriggerClientEvent('28_gang:nearbyTerritoriesReceived', source, nearbyTerritories)
end)

-- Admin commands pro zone management
RegisterCommand('gang_assign', function(source, args, rawCommand)
    if source ~= 0 and not exports.qbx_core:HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local territoryId = tonumber(args[1])
    local gang = args[2]
    
    if not territoryId or not gang then
        local message = 'Použití: /gang_assign [territory_id] [gang_name|none]'
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'error', message)
        end
        return
    end
    
    TriggerEvent('28_gang:assignTerritoryToGang', territoryId, gang)
end, false)

RegisterCommand('gang_territories', function(source, args, rawCommand)
    if source ~= 0 and not exports.qbx_core:HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local gang = args[1]
    
    if gang then
        -- Zobrazit teritoria konkrétního gangu
        local gangTerritories = GetGangTerritories(gang)
        local message = '^3=== TERITORIA GANGU ' .. string.upper(gang) .. ' ===^7\n'
        
        local count = 0
        for id, territory in pairs(gangTerritories) do
            count = count + 1
            message = message .. string.format('^2ID: %s^7 | ^4Radius: %.1fm^7 | ^5Coords: %.1f, %.1f, %.1f^7\n', 
                id, territory.radius, territory.coords.x, territory.coords.y, territory.coords.z)
        end
        
        if count == 0 then
            message = message .. '^1Žádná teritoria nenalezena^7'
        else
            message = message .. '^6Celkem: ' .. count .. ' teritorií^7'
        end
        
        if source == 0 then
            print(message)
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 255, 255},
                multiline = true,
                args = {"Gang System", message}
            })
        end
    else
        -- Zobrazit všechna teritoria seskupená podle gangů
        local gangStats = {}
        
        for id, territory in pairs(territories) do
            local gang = territory.gang
            if not gangStats[gang] then
                gangStats[gang] = {count = 0, territories = {}}
            end
            gangStats[gang].count = gangStats[gang].count + 1
            gangStats[gang].territories[id] = territory
        end
        
        local message = '^3=== PŘEHLED VŠECH TERITORIÍ ===^7\n'
        
        for gang, stats in pairs(gangStats) do
            local gangName = gang == 'none' and 'Žádný gang' or (Config.Gangs[gang] and Config.Gangs[gang].name or gang)
            message = message .. string.format('^2%s^7: ^6%d teritorií^7\n', gangName, stats.count)
        end
        
        if source == 0 then
            print(message)
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 255, 255},
                multiline = true,
                args = {"Gang System", message}
            })
        end
    end
end, false)

-- Export funkcí
exports('GetTerritoryOwner', GetTerritoryOwner)
exports('GetGangTerritories', GetGangTerritories)
exports('GetNearbyTerritories', GetNearbyTerritories)
exports('ValidateTerritoryPosition', ValidateTerritoryPosition)
exports('IsPlayerInTerritory', IsPlayerInTerritory)
