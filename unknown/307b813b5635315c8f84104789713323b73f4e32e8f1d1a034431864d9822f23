let currentMenu = null;
let currentData = {};

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // ESC key to close menu
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeMenu();
        }
    });
});

// NUI Message handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openMenu':
            openMenu(data.type, data.data);
            break;
        case 'closeMenu':
            closeMenu();
            break;
        case 'updateMenu':
            updateMenu(data.data);
            break;
        case 'showTerritoryNotification':
            showTerritoryNotification(data.data);
            break;
    }
});

function openMenu(menuType, data) {
    currentMenu = menuType;
    currentData = data;
    
    // Hide all menus
    document.querySelectorAll('.menu').forEach(menu => {
        menu.classList.add('hidden');
    });
    
    // Show app container
    document.getElementById('app').classList.remove('hidden');
    
    // Show specific menu
    const menuElement = document.getElementById(menuType);
    if (menuElement) {
        menuElement.classList.remove('hidden');
        populateMenu(menuType, data);
    }
}

function closeMenu() {
    document.getElementById('app').classList.add('hidden');
    currentMenu = null;
    currentData = {};
    
    // Send close event to client
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({})
    });
}

function populateMenu(menuType, data) {
    switch(menuType) {
        case 'mainMenu':
            populateMainMenu(data);
            break;
        case 'territoryInfo':
            populateTerritoryInfo(data);
            break;
        case 'gangStats':
            populateGangStats(data);
            break;
        case 'sprayMenu':
            populateSprayMenu(data);
            break;
        case 'reputationMenu':
            populateReputationMenu(data);
            break;
    }
}

function populateMainMenu(data) {
    if (data.gang) {
        document.getElementById('gangName').textContent = data.gang.label || data.gang.name;
    }
    
    // Update gang stats (placeholder)
    document.getElementById('gangStats').textContent = 'Teritoria: 0 | Spreje: 0';
}

function populateTerritoryInfo(data) {
    if (data.gang) {
        document.getElementById('territoryGang').textContent = `Gang: ${data.gang.name}`;
    }
    
    if (data.territory && data.territory.coords) {
        const coords = data.territory.coords;
        document.getElementById('territoryCoords').textContent = 
            `Souřadnice: ${coords.x.toFixed(1)}, ${coords.y.toFixed(1)}, ${coords.z.toFixed(1)}`;
        document.getElementById('territoryRadius').textContent = 
            `Poloměr: ${data.territory.radius}m`;
    }
}

function populateGangStats(data) {
    if (data.stats) {
        document.getElementById('totalTerritories').textContent = data.stats.territories || 0;
        document.getElementById('totalSprays').textContent = data.stats.sprays || 0;
    }
    
    // Calculate controlled area percentage (placeholder)
    document.getElementById('controlledArea').textContent = '0%';
    
    // Populate territory list
    const territoryList = document.getElementById('territoryList');
    territoryList.innerHTML = '';
    
    if (data.territories) {
        Object.entries(data.territories).forEach(([id, territory]) => {
            const territoryItem = document.createElement('div');
            territoryItem.className = 'territory-item';
            territoryItem.innerHTML = `
                <p><strong>ID ${id}:</strong> Poloměr ${territory.radius}m</p>
                <small>Coords: ${territory.coords.x.toFixed(1)}, ${territory.coords.y.toFixed(1)}, ${territory.coords.z.toFixed(1)}</small>
            `;
            territoryList.appendChild(territoryItem);
        });
    } else {
        territoryList.innerHTML = '<p>Žádná teritoria</p>';
    }
}

function populateSprayMenu(data) {
    if (data.territory) {
        const ownerText = data.territory.gang === 'none' ? 'Žádný' : data.territory.gang;
        document.getElementById('currentTerritoryOwner').textContent = `Vlastník: ${ownerText}`;
    }

    // Update spray progress
    if (data.stats && data.requiredSprays) {
        const currentSprays = Object.values(data.stats).reduce((sum, count) => sum + count, 0);
        const progress = Math.min((currentSprays / data.requiredSprays) * 100, 100);

        document.getElementById('sprayProgress').style.width = `${progress}%`;
        document.getElementById('sprayProgressText').textContent =
            `${currentSprays} / ${data.requiredSprays} sprejů`;
    }
}

function populateReputationMenu(data) {
    // Update current reputation
    if (data.currentReputation !== undefined) {
        document.getElementById('currentReputationValue').textContent = data.currentReputation;

        const progress = (data.currentReputation / data.maxReputation) * 100;
        document.getElementById('reputationProgress').style.width = `${progress}%`;

        // Update rank
        const rank = getReputationRank(data.currentReputation, data.maxReputation);
        document.getElementById('reputationRank').textContent = rank;
    }

    // Populate gang leaderboard
    const leaderboardList = document.getElementById('gangLeaderboard');
    leaderboardList.innerHTML = '';

    if (data.leaderboard && data.leaderboard.length > 0) {
        data.leaderboard.forEach((player, index) => {
            const item = document.createElement('div');
            item.className = 'leaderboard-item';

            item.innerHTML = `
                <div class="player-info">
                    <span class="player-name">#${index + 1} ${player.citizenid}</span>
                    <span class="player-stats">Spreje: ${player.total_sprays} | Drogy: ${player.total_drug_sales}</span>
                </div>
                <div class="reputation-value">${player.reputation}</div>
            `;

            leaderboardList.appendChild(item);
        });
    } else {
        leaderboardList.innerHTML = '<p>Žádní hráči v žebříčku</p>';
    }

    // Populate top gangs
    const topGangsList = document.getElementById('topGangsList');
    topGangsList.innerHTML = '';

    if (data.topGangs && data.topGangs.length > 0) {
        data.topGangs.forEach((gang, index) => {
            const item = document.createElement('div');
            item.className = 'top-gang-item';

            item.innerHTML = `
                <div class="gang-info">
                    <span class="gang-name">#${index + 1} ${gang.gang}</span>
                    <span class="gang-stats">Členové: ${gang.member_count} | Teritoria: ${gang.total_territories_taken}</span>
                </div>
                <div class="reputation-value">${gang.total_reputation}</div>
            `;

            topGangsList.appendChild(item);
        });
    } else {
        topGangsList.innerHTML = '<p>Žádné gangy v žebříčku</p>';
    }
}

function getReputationRank(reputation, maxReputation) {
    const percentage = reputation / maxReputation;

    if (percentage >= 0.9) return 'Legenda';
    if (percentage >= 0.8) return 'Veterán';
    if (percentage >= 0.6) return 'Respektovaný';
    if (percentage >= 0.4) return 'Zkušený';
    if (percentage >= 0.2) return 'Začátečník';
    return 'Nováček';
}

function updateMenu(data) {
    if (currentMenu) {
        populateMenu(currentMenu, { ...currentData, ...data });
    }
}

function showTerritoryNotification(data) {
    const notification = document.getElementById('territoryNotification');
    const title = document.getElementById('notificationTitle');
    const message = document.getElementById('notificationMessage');
    
    if (data.gang) {
        title.textContent = data.gang.name;
        title.style.color = `rgb(${data.gang.color.r}, ${data.gang.color.g}, ${data.gang.color.b})`;
    }
    
    message.textContent = data.message || 'Vstoupil jsi do teritoria';
    
    notification.classList.remove('hidden');
    
    // Auto hide after 5 seconds
    setTimeout(() => {
        notification.classList.add('hidden');
    }, 5000);
}

// Menu action functions
function showGangStats() {
    fetch(`https://${GetParentResourceName()}/getTerritoryDetails`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({ action: 'gangStats' })
    });
}

function showTerritoryInfo() {
    fetch(`https://${GetParentResourceName()}/getTerritoryDetails`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({ action: 'territoryInfo' })
    });
}

function showSprayMenu() {
    closeMenu();
    // This will be handled by the client-side UI module
}

function startSpray() {
    fetch(`https://${GetParentResourceName()}/sprayTerritory`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({})
    });
}

function defendTerritory() {
    fetch(`https://${GetParentResourceName()}/defendTerritory`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({})
    });
}

// Utility function to get resource name
function GetParentResourceName() {
    return window.location.hostname;
}
