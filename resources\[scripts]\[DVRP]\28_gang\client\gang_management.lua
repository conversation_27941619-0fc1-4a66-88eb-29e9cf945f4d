local GangManagement = {}
-- QBX Core je dostupný přes exports.qbx_core
local currentGangInfo = nil

function GangManagement.GetCurrentGangInfo()
    return currentGangInfo
end

function GangManagement.RequestGangInfo()
    TriggerServerEvent('28_gang:requestPlayerGangInfo')
end

function GangManagement.ShowGangManagementMenu()
    local PlayerData = exports.qbx_core.PlayerData

    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return
    end
    
    -- Požádat o aktuální gang info
    GangManagement.RequestGangInfo()
    
    -- Otevřít menu po krátké pauze
    CreateThread(function()
        Wait(500)
        local UI = exports[GetCurrentResourceName()]:UI()
        if UI then
            UI.OpenMenu('gangManagementMenu', {
                gang = PlayerData.gang,
                gangInfo = currentGangInfo,
                playerRank = currentGangInfo and currentGangInfo.rank or 'member'
            })
        end
    end)
end

function GangManagement.HasPermission(permission)
    if not currentGangInfo or not currentGangInfo.rank then return false end
    
    local rank = currentGangInfo.rank
    local rankData = Config.GangRanks[rank]
    
    if not rankData or not rankData.permissions then return false end
    
    for _, perm in pairs(rankData.permissions) do
        if perm == permission then
            return true
        end
    end
    
    return false
end

function GangManagement.GetRankLevel(rank)
    if Config.GangRanks[rank] then
        return Config.GangRanks[rank].level
    end
    return 0
end

function GangManagement.CanPromoteTo(targetRank)
    if not currentGangInfo or not currentGangInfo.rank then return false end
    
    local myLevel = GangManagement.GetRankLevel(currentGangInfo.rank)
    local targetLevel = GangManagement.GetRankLevel(targetRank)
    
    -- Můžeš povýšit pouze na nižší nebo stejnou úroveň než máš ty
    return myLevel >= targetLevel and GangManagement.HasPermission('promote')
end

-- Event handlers
RegisterNetEvent('28_gang:playerGangInfoReceived', function(gangInfo)
    currentGangInfo = gangInfo
end)

RegisterNetEvent('28_gang:gangInfoUpdated', function(gangInfo)
    currentGangInfo = gangInfo
    
    -- Aktualizovat UI pokud je otevřené
    local UI = exports[GetCurrentResourceName()]:UI()
    if UI then
        UI.UpdateMenu({
            gangInfo = gangInfo,
            playerRank = gangInfo and gangInfo.rank or 'member'
        })
    end
end)

-- Commands
RegisterCommand('gangmanage', function()
    GangManagement.ShowGangManagementMenu()
end, false)

RegisterCommand('mygang', function()
    local PlayerData = exports.qbx_core.PlayerData

    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return
    end
    
    if currentGangInfo then
        local rankLabel = Config.GangRanks[currentGangInfo.rank] and Config.GangRanks[currentGangInfo.rank].name or currentGangInfo.rank
        
        TriggerEvent('chat:addMessage', {
            color = {255, 165, 0},
            multiline = true,
            args = {"Gang Info", string.format('Gang: %s\nHodnost: %s\nReputační požadavek: %d', 
                PlayerData.gang.label or PlayerData.gang.name, 
                rankLabel,
                currentGangInfo.reputation_requirement or 100)}
        })
    else
        GangManagement.RequestGangInfo()
        TriggerEvent('28_gang:notification', 'info', 'Načítám informace o gangu...')
    end
end, false)

-- Key mapping
RegisterKeyMapping('gangmanage', 'Gang Management Menu', 'keyboard', 'F9')

-- Inicializace při načtení
CreateThread(function()
    while not exports.qbx_core do
        Wait(100)
    end

    -- Počkat na načtení hráče
    while not exports.qbx_core.PlayerData.citizenid do
        Wait(1000)
    end

    -- Načíst gang info
    GangManagement.RequestGangInfo()
end)

-- Automatické obnovení gang info každých 2 minuty
CreateThread(function()
    while true do
        Wait(120000) -- 2 minuty
        local PlayerData = exports.qbx_core.PlayerData
        if PlayerData.gang and PlayerData.gang.name ~= 'none' then
            GangManagement.RequestGangInfo()
        end
    end
end)

-- Export pro ostatní moduly
exports('GangManagement', GangManagement)
return GangManagement
